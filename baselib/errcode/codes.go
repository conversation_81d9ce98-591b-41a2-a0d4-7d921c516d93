package errcode

// 系统级错误码
var (
	ErrOK               = New(0, "success")
	ErrNotOK            = New(-1, "error")
	ErrorInternal       = New(101, "服务器内部错误")
	ErrorParam          = New(102, "参数错误")
	ErrUnauthorized     = New(103, "未授权")
	ErrNotFound         = New(104, "资源不存在")
	ErrTooManyRequests  = New(105, "请求过于频繁")
	ErrorCanceled       = New(106, "请求被取消")
	ErrorRateLimit      = New(107, "操作过于频繁，请稍后再试")
	ErrPermissionDenied = New(108, "无权限操作")
	ErrConfigNotFound   = New(109, "配置不存在")
)

// 数据库相关错误 (300-399)
var (
	ErrDBConnectionFailed = New(300, "数据库连接失败")
	ErrDBQueryFailed      = New(301, "数据库查询失败")
	ErrDBInsertFailed     = New(302, "数据库插入失败")
	ErrDBUpdateFailed     = New(303, "数据库更新失败")
	ErrDBDeleteFailed     = New(304, "数据库删除失败")
)

// 缓存相关错误 (400-499)
var (
	ErrCacheConnectionFailed = New(400, "缓存连接失败")
	ErrCacheGetFailed        = New(401, "缓存获取失败")
	ErrCacheSetFailed        = New(402, "缓存设置失败")
	ErrCacheDeleteFailed     = New(403, "缓存删除失败")
)

// 外部服务错误 (500-599)
var (
	ErrExternalServiceFailed = New(500, "外部服务调用失败")
	ErrServiceTimeout        = New(501, "服务调用超时")
)

// 用户模块错误码 (1000-1999)
var (
	ErrUserNotFound          = New(1000, "用户不存在")
	ErrUserAlreadyExists     = New(1001, "用户已存在")
	ErrInvalidPassword       = New(1002, "密码错误")
	ErrInvalidVerifyCode     = New(1003, "手机号或验证码错误，请重新输入")
	ErrVerifyCodeExpired     = New(1004, "验证码已过期")
	ErrTooManyVerifyAttempts = New(1005, "验证次数过多")
	ErrPhoneAlreadyBound     = New(1006, "手机号已绑定")
	ErrUserBlocked           = New(1007, "账号已禁用")
	ErrUserNotBlocked        = New(1008, "账号未被禁用")
	ErrUserCreateFailed      = New(1009, "用户创建失败")
	ErrUserUpdateFailed      = New(1010, "用户更新失败")
	ErrUserQueryFailed       = New(1011, "用户查询失败")
	ErrRegisterInfoNotFound  = New(1012, "用户注册信息未找到")
	ErrPhoneNotMatch         = New(1013, "注册手机号不匹配")
	ErrDailyCheckInFailed    = New(1014, "每日签到失败")
	ErrUserDeleted           = New(1015, "用户已注销")

	// 用户认证相关错误码 (1100-1199)
	ErrUserAuthNotFound     = New(1100, "用户认证信息不存在")
	ErrUserAuthCreateFailed = New(1101, "用户认证信息创建失败")
	ErrUserAuthUpdateFailed = New(1102, "用户认证信息更新失败")
	ErrUserAuthQueryFailed  = New(1103, "用户认证信息查询失败")

	// 用户注销历史相关错误码已移除 (1200-1299)
	// 注：用户注销改为软删除，不再需要单独的注销历史表

	// Token相关错误码 (1300-1399)
	ErrTokenExpired         = New(1300, "token已过期")
	ErrInvalidToken         = New(1301, "无效的token")
	ErrTokenCreateFailed    = New(1302, "token生成失败")
	ErrTokenRequired        = New(1303, "token不能为空")
	ErrTokenTooOld          = New(1304, "token已超过最大刷新时间")
	ErrTokenTypeMismatch    = New(1305, "无效的token类型")
	ErrDeviceMismatch       = New(1306, "设备信息不匹配")
	ErrInvalidSigningMethod = New(1307, "无效的签名方法")
	ErrTokenRevoked         = New(1308, "token已被吊销")
	ErrDeviceFingerprint    = New(1309, "设备指纹不匹配")
	ErrTokenInvalid         = New(1310, "token已被封禁")
	ErrTokenMissing         = New(1311, "用户未登录")

	// 签名验证相关错误码 (1400-1499)
	ErrSignatureMissing     = New(1400, "签名参数缺失")
	ErrSignatureInvalid     = New(1401, "签名验证失败")
	ErrTimestampMissing     = New(1402, "时间戳参数缺失")
	ErrTimestampInvalid     = New(1403, "时间戳格式错误")
	ErrTimestampExpired     = New(1404, "请求时间戳已过期")
	ErrSignatureDisabled    = New(1405, "签名验证未启用")
	ErrSignatureConfigError = New(1406, "签名配置错误")
	ErrPlatformNotSupported = New(1407, "不支持的平台")
	ErrRequiredFieldMissing = New(1408, "必需的签名字段缺失")
	ErrUserNotLoggedIn      = New(1311, "用户未登录")
	ErrDeviceNotFound       = New(1312, "设备不存在")

	// 短信相关错误码
	ErrSmsSendTooFrequent = New(1400, "发送过于频繁")
	ErrSmsSendFailed      = New(1401, "短信发送失败")

	// review
	ErrUserAvatarReviewFailed         = New(1500, "您的用户头像未通过审核，请修改后提交")
	ErrUserNicknameReviewFailed       = New(1501, "您的昵称未通过审核，请修改后提交")
	ErrUserBackgroundReviewFailed     = New(1502, "您的背景图片未通过审核，请修改后提交")
	ErrUserVoiceSignatureReviewFailed = New(1503, "您的语音签名未通过审核，请修改后提交")

	// 用户审核记录相关错误码 (1600-1699)
	ErrUserAuditRecordNotFound     = New(1600, "用户审核记录不存在")
	ErrUserAuditRecordCreateFailed = New(1601, "用户审核记录创建失败")
	ErrUserAuditRecordUpdateFailed = New(1602, "用户审核记录更新失败")
	ErrUserAuditRecordQueryFailed  = New(1603, "用户审核记录查询失败")
	ErrUserAuditInvalidType        = New(1604, "无效的审核类型")
	ErrUserAuditInvalidStatus      = New(1605, "无效的审核状态")
	ErrUserAuditProcessFailed      = New(1606, "用户审核处理失败")
)

// 剧本服务错误码 (2000-2999)
var (
	// 剧本相关错误 (2000-2099)
	ErrScriptNotFound              = New(2000, "剧本不存在")
	ErrScriptCreateFailed          = New(2001, "创建剧本失败")
	ErrScriptUpdateFailed          = New(2002, "更新剧本失败")
	ErrScriptDeleteFailed          = New(2003, "删除剧本失败")
	ErrScriptQueryFailed           = New(2004, "查询剧本失败")
	ErrScriptAuditFailed           = New(2005, "剧本审核失败")
	ErrScriptNotPublished          = New(2006, "剧本未发布")
	ErrScriptDeleted               = New(2007, "剧本已删除")
	ErrScriptUserStat              = New(2008, "用户剧本统计失败")
	ErrScriptUserData              = New(2009, "获取用户剧本数据失败")
	ErrScriptCharacterCreateFailed = New(2010, "创建剧本角色关系失败")
	ErrScriptCharacterQueryFailed  = New(2011, "查询剧本角色关系失败")
	ErrScriptCharacterDeleteFailed = New(2012, "删除剧本角色关系失败")

	// 剧本审核
	ErrScriptReviewTitle        = New(2030, "剧本标题审核失败")
	ErrScriptReviewCover        = New(2031, "剧本封面审核失败")
	ErrScriptReviewLine         = New(2032, "剧本台词审核失败")
	ErrScriptReviewTopic        = New(2033, "剧本话题审核失败")
	ErrScriptReviewDubbingText  = New(2034, "剧本配音文本审核失败")
	ErrScriptReviewDubbingVoice = New(2035, "剧本配音语音审核失败")
	ErrScriptReviewCommentText  = New(2036, "剧本评论文本审核失败")
	ErrScriptReviewCommentVoice = New(2037, "剧本评论语音审核失败")

	// 剧本角色相关错误 (2100-2199)
	ErrCharacterNotFound      = New(2100, "角色不存在")
	ErrCharacterCreateFailed  = New(2101, "创建角色失败")
	ErrCharacterUpdateFailed  = New(2102, "更新角色失败")
	ErrCharacterDeleteFailed  = New(2103, "删除角色失败")
	ErrCharacterQueryFailed   = New(2104, "查询角色失败")
	ErrUserCharacterNotFound  = New(2105, "用户角色不存在")
	ErrUserCharacterSetFailed = New(2106, "设置用户角色失败")
	ErrIPHasCharacters        = New(2107, "IP下存在角色，不允许删除")

	// 角色素材相关错误 (2110-2119)
	ErrCharacterMaterialNotFound     = New(2110, "角色素材不存在")
	ErrCharacterMaterialCreateFailed = New(2111, "创建角色素材失败")
	ErrCharacterMaterialUpdateFailed = New(2112, "更新角色素材失败")
	ErrCharacterMaterialDeleteFailed = New(2113, "删除角色素材失败")
	ErrCharacterMaterialQueryFailed  = New(2114, "查询角色素材失败")

	// 角色资产相关错误 (2120-2129)
	ErrCharacterAssetNotFound     = New(2120, "角色资源包不存在")
	ErrCharacterAssetCreateFailed = New(2121, "创建角色资源包失败")
	ErrCharacterAssetUpdateFailed = New(2122, "更新角色资源包失败")
	ErrCharacterAssetDeleteFailed = New(2123, "删除角色资源包失败")
	ErrCharacterAssetQueryFailed  = New(2124, "查询角色资源包失败")

	// 剧本台词相关错误 (2200-2299)
	ErrLineNotFound     = New(2200, "台词不存在")
	ErrLineCreateFailed = New(2201, "创建台词失败")
	ErrLineUpdateFailed = New(2202, "更新台词失败")
	ErrLineDeleteFailed = New(2203, "删除台词失败")
	ErrLineQueryFailed  = New(2204, "查询台词失败")

	// 配音相关错误 (2300-2399)
	ErrDubbingNotFound     = New(2300, "配音不存在")
	ErrDubbingCreateFailed = New(2301, "创建配音失败")
	ErrDubbingUpdateFailed = New(2302, "更新配音失败")
	ErrDubbingDeleteFailed = New(2303, "删除配音失败")
	ErrDubbingQueryFailed  = New(2304, "查询配音失败")
	ErrDubbingSetTopFailed = New(2305, "设置配音置顶失败")

	// 话题相关错误 (2400-2499)
	ErrTopicNotFound     = New(2400, "话题不存在")
	ErrTopicCreateFailed = New(2401, "创建话题失败")
	ErrTopicUpdateFailed = New(2402, "更新话题失败")
	ErrTopicDeleteFailed = New(2403, "删除话题失败")
	ErrTopicQueryFailed  = New(2404, "查询话题失败")
	ErrTopicInvalidName  = New(2405, "话题名称无效")

	// 评论相关错误 (2500-2599)
	ErrCommentNotFound      = New(2500, "评论不存在")
	ErrCommentCreateFailed  = New(2501, "创建评论失败")
	ErrCommentUpdateFailed  = New(2502, "更新评论失败")
	ErrCommentDeleteFailed  = New(2503, "删除评论失败")
	ErrCommentQueryFailed   = New(2504, "查询评论失败")
	ErrCommentNotParent     = New(2505, "非父级评论")
	ErrCommentDepthExceeded = New(2506, "评论层级超出限制")
	ErrCommentNotTopLevel   = New(2507, "只有一级评论才能置顶")
	ErrCommentSetTopFailed  = New(2508, "设置评论置顶失败")
	ErrASRProcessing        = New(2509, "ASR处理中")

	// 点赞相关错误 (2600-2699)
	ErrLikeNotFound      = New(2600, "点赞不存在")
	ErrLikeCreateFailed  = New(2601, "点赞失败")
	ErrLikeDeleteFailed  = New(2602, "取消点赞失败")
	ErrLikeQueryFailed   = New(2603, "查询点赞失败")
	ErrLikeAlreadyExists = New(2604, "已经点赞")

	// 关注相关错误 (2700-2799)
	ErrFollowNotFound      = New(2700, "关注不存在")
	ErrFollowCreateFailed  = New(2701, "关注失败")
	ErrFollowDeleteFailed  = New(2702, "取消关注失败")
	ErrFollowQueryFailed   = New(2703, "查询关注失败")
	ErrFollowAlreadyExists = New(2704, "已经关注")

	// IP相关错误 (2800-2899)
	ErrIPNotFound     = New(2800, "IP不存在")
	ErrIPCreateFailed = New(2801, "创建IP失败")
	ErrIPUpdateFailed = New(2802, "更新IP失败")
	ErrIPDeleteFailed = New(2803, "删除IP失败")
	ErrIPQueryFailed  = New(2804, "查询IP失败")

	// 封面相关错误 (2850-2899)
	ErrCoverNotFound     = New(2850, "封面不存在")
	ErrCoverCreateFailed = New(2851, "创建封面失败")
	ErrCoverUpdateFailed = New(2852, "更新封面失败")
	ErrCoverDeleteFailed = New(2853, "删除封面失败")
	ErrCoverQueryFailed  = New(2854, "查询封面失败")

	// 举报相关错误 (2900-2999)
	ErrReportNotFound      = New(2900, "举报不存在")
	ErrReportCreateFailed  = New(2901, "举报失败")
	ErrReportUpdateFailed  = New(2902, "更新举报失败")
	ErrReportDeleteFailed  = New(2903, "删除举报失败")
	ErrReportQueryFailed   = New(2904, "查询举报失败")
	ErrReportAlreadyExists = New(2905, "已经举报")
	ErrReportReasonInvalid = New(2906, "举报原因无效")
)

// 朋友圈模块错误码 (3000-3999)
var (
	// 朋友圈相关错误码 (3100-3199)
	ErrMomentNotFound     = New(3100, "朋友圈不存在")
	ErrMomentCreateFailed = New(3101, "朋友圈创建失败")
	ErrMomentUpdateFailed = New(3102, "朋友圈更新失败")
	ErrMomentDeleteFailed = New(3103, "朋友圈删除失败")
	ErrMomentQueryFailed  = New(3104, "朋友圈查询失败")
)

// 账户相关
var (
	ErrAccountBalanceNotEnough = New(2400, "账户余额不足")
)
