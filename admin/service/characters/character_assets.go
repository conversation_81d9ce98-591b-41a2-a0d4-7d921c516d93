package characters

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/characters"
	charactersReq "github.com/flipped-aurora/gin-vue-admin/server/model/characters/request"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
)

type CharacterAssetsService struct{}

// CreateCharacterAssets 创建characterAssets表记录
// Author [yourname](https://github.com/yourname)
func (characterAssetsService *CharacterAssetsService) CreateCharacterAssets(ctx context.Context, characterAssets *characters.CharacterAssets) (err error) {
	err = global.GVA_DB.Create(characterAssets).Error
	return err
}

// DeleteCharacterAssets 删除characterAssets表记录
// Author [yourname](https://github.com/yourname)
func (characterAssetsService *CharacterAssetsService) DeleteCharacterAssets(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Model(&characters.CharacterAssets{}).Where("id = ?", id).Update("status", 2).Error
	return err
}

// DeleteCharacterAssetsByIds 批量删除characterAssets表记录
// Author [yourname](https://github.com/yourname)
func (characterAssetsService *CharacterAssetsService) DeleteCharacterAssetsByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Model(&characters.CharacterAssets{}).Where("id in ?", ids).Update("status", 2).Error
	return err
}

// UpdateCharacterAssets 更新characterAssets表记录
// Author [yourname](https://github.com/yourname)
func (characterAssetsService *CharacterAssetsService) UpdateCharacterAssets(ctx context.Context, characterAssets characters.CharacterAssets) (err error) {
	err = global.GVA_DB.Model(&characters.CharacterAssets{}).Where("id = ?", characterAssets.Id).Updates(&characterAssets).Error
	return err
}

// GetCharacterAssets 根据id获取characterAssets表记录
// Author [yourname](https://github.com/yourname)
func (characterAssetsService *CharacterAssetsService) GetCharacterAssets(ctx context.Context, id string) (characterAssets characters.CharacterAssets, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&characterAssets).Error
	return
}

// GetCharacterAssetsInfoList 分页获取characterAssets表记录
// Author [yourname](https://github.com/yourname)
func (characterAssetsService *CharacterAssetsService) GetCharacterAssetsInfoList(ctx context.Context, info charactersReq.CharacterAssetsSearch) (list []*characters.CharacterAssets, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&characters.CharacterAssets{})
	var characterAssetss []*characters.CharacterAssets
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.CharacterId > 0 {
		db = db.Where("character_id = ?", info.CharacterId)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&characterAssetss).Error
	return characterAssetss, total, err
}

// UpdatePresetStatus 更新预设状态记录
// Author [yourname](https://github.com/yourname)
func (characterAssetsService *CharacterAssetsService) UpdatePresetStatus(ctx context.Context, updatePresetStatus charactersReq.UpdatePresetStatus) (err error) {
	err = global.GVA_DB.Model(&characters.CharacterAssets{}).Where("id = ?", updatePresetStatus.Id).Updates(map[string]interface{}{
		"preset_status": updatePresetStatus.PresetStatus,
		"updated_at":    util.NowTimeMillis(),
	}).Error
	return err
}
